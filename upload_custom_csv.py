#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to upload any CSV file as an attachment to a Braintrust dataset.
Allows you to specify the CSV file, project, and dataset names.
"""

import os
import argparse
import csv
from braintrust import Attachment, init_dataset


def analyze_csv_structure(csv_file_path: str):
    """
    Analyze the CSV file to understand its structure.
    
    Args:
        csv_file_path: Path to the CSV file
        
    Returns:
        dict: Information about the CSV structure
    """
    with open(csv_file_path, 'r', encoding='utf-8') as file:
        # Read first few lines to understand structure
        sample = file.read(1024)
        file.seek(0)
        
        # Use csv.Sniffer to detect delimiter
        sniffer = csv.Sniffer()
        delimiter = sniffer.sniff(sample).delimiter
        
        # Read the CSV to get column info
        reader = csv.DictReader(file, delimiter=delimiter)
        columns = reader.fieldnames or []
        
        # Count rows
        file.seek(0)
        row_count = sum(1 for _ in csv.reader(file, delimiter=delimiter)) - 1  # -1 for header
        
        return {
            'columns': columns,
            'row_count': row_count,
            'delimiter': delimiter,
            'filename': os.path.basename(csv_file_path)
        }


def create_generic_tasks(csv_info: dict):
    """
    Create generic analysis tasks based on CSV structure.
    
    Args:
        csv_info: Information about the CSV structure
        
    Returns:
        list: List of task dictionaries
    """
    columns = csv_info['columns']
    filename = csv_info['filename']
    
    tasks = [
        {
            "description": "Provide a summary of the data structure and contents",
            "expected": f"CSV contains {csv_info['row_count']} rows with columns: {', '.join(columns)}"
        },
        {
            "description": "Analyze the first 5 rows of data",
            "expected": "Analysis of the first 5 data rows with key insights"
        }
    ]
    
    # Add column-specific tasks if we can identify numeric or categorical columns
    if len(columns) > 1:
        tasks.append({
            "description": f"Compare and analyze the relationship between {columns[0]} and {columns[1] if len(columns) > 1 else 'other columns'}",
            "expected": f"Analysis of relationships between {columns[0]} and other variables"
        })
    
    # Add a counting task
    tasks.append({
        "description": "Count unique values in each column",
        "expected": "Summary of unique value counts for each column"
    })
    
    # Add a data quality task
    tasks.append({
        "description": "Check for missing values, duplicates, and data quality issues",
        "expected": "Data quality report with any issues identified"
    })
    
    return tasks


def upload_csv_to_dataset(project_name: str, dataset_name: str, csv_file_path: str, 
                         custom_tasks: list = None):
    """
    Upload a CSV file as attachments to a Braintrust dataset.
    
    Args:
        project_name: Name of the Braintrust project
        dataset_name: Name of the dataset to create
        csv_file_path: Path to the CSV file to upload
        custom_tasks: Optional list of custom task descriptions
    """
    
    # Verify the CSV file exists
    if not os.path.exists(csv_file_path):
        raise FileNotFoundError(f"CSV file not found: {csv_file_path}")
    
    print(f"📁 Analyzing CSV file: {csv_file_path}")
    csv_info = analyze_csv_structure(csv_file_path)
    
    print(f"📊 CSV Info:")
    print(f"   - Filename: {csv_info['filename']}")
    print(f"   - Columns: {csv_info['columns']}")
    print(f"   - Rows: {csv_info['row_count']}")
    print(f"   - Delimiter: '{csv_info['delimiter']}'")
    
    print(f"\n🏗️  Creating dataset '{dataset_name}' in project '{project_name}'...")
    
    # Initialize the dataset
    dataset = init_dataset(project_name, dataset_name)
    
    # Use custom tasks if provided, otherwise generate generic ones
    if custom_tasks:
        tasks = [{"description": task, "expected": "Analysis result"} for task in custom_tasks]
    else:
        tasks = create_generic_tasks(csv_info)
    
    # Create dataset rows
    for i, task in enumerate(tasks, 1):
        print(f"📝 Creating row {i}/{len(tasks)}: {task['description'][:50]}...")
        
        dataset.insert(
            input={
                "task": task["description"],
                "csv_file": Attachment(
                    filename=csv_info['filename'],
                    content_type="text/csv",
                    data=csv_file_path
                ),
                "file_info": {
                    "columns": csv_info['columns'],
                    "row_count": csv_info['row_count'],
                    "delimiter": csv_info['delimiter']
                }
            },
            expected=task["expected"]
        )
    
    # Flush to ensure all data is uploaded
    print("💾 Uploading dataset...")
    dataset.flush()
    
    print(f"✅ Successfully created dataset with {len(tasks)} rows!")
    print(f"📋 Dataset summary:")
    print(dataset.summarize())
    
    return dataset


def main():
    parser = argparse.ArgumentParser(
        description="Upload any CSV file as attachments to a Braintrust dataset",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Upload sample_data.csv to a new dataset
  python upload_custom_csv.py "My Project" "Employee Data" sample_data.csv
  
  # Upload with custom analysis tasks
  python upload_custom_csv.py "Analysis Project" "Sales Data" sales.csv \\
    --tasks "Calculate total revenue" "Find top customers" "Analyze trends"
  
  # Just upload without custom tasks (will generate generic ones)
  python upload_custom_csv.py "Data Project" "Generic Analysis" data.csv
        """
    )
    
    parser.add_argument("project_name", help="Name of the Braintrust project")
    parser.add_argument("dataset_name", help="Name of the dataset to create")
    parser.add_argument("csv_file", help="Path to the CSV file to upload")
    parser.add_argument("--tasks", nargs="*", help="Custom analysis tasks (optional)")
    
    args = parser.parse_args()
    
    try:
        # Upload the CSV file
        dataset = upload_csv_to_dataset(
            args.project_name,
            args.dataset_name, 
            args.csv_file,
            args.tasks
        )
        
        print(f"\n🎉 Success! Your CSV file has been uploaded to Braintrust.")
        print(f"🌐 You can now use this dataset in the playground at:")
        print(f"   https://www.braintrust.dev/app/{args.project_name.replace(' ', '%20')}/datasets")
        print(f"\n💡 Tips for using in the playground:")
        print(f"   - The CSV file will be available as an attachment in each row")
        print(f"   - Use prompts like: 'Analyze the attached CSV file and {{{{task}}}}'")
        print(f"   - The file_info contains metadata about columns and structure")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
