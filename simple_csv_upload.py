#!/usr/bin/env python3
"""
Barebones example: Create a dataset with a single row containing a CSV attachment.
"""

import sys
from braintrust import Attachment, init_dataset


def main():
    if len(sys.argv) != 4:
        print("Usage: python simple_csv_upload.py <project_name> <dataset_name> <csv_file_path>")
        print("Example: python simple_csv_upload.py 'My Project' 'My Dataset' sample_data.csv")
        return 1
    
    project_name = sys.argv[1]
    dataset_name = sys.argv[2]
    csv_file_path = sys.argv[3]
    
    # Create dataset
    dataset = init_dataset(project_name, dataset_name)
    
    # Add single row with CSV attachment
    dataset.insert(
        input={
            "csv_data": Attachment(
                filename="data.csv",
                content_type="text/csv",
                data=csv_file_path
            )
        },
        expected="Analysis result"
    )
    
    # Upload
    dataset.flush()
    
    print(f"✅ Created dataset '{dataset_name}' with CSV attachment")
    return 0


if __name__ == "__main__":
    exit(main())
